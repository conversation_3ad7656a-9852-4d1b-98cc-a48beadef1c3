@echo off
echo Registering MyCom Office Add-in (User-level registration)...
echo.

REM Register the COM component with user-level registration
regsvr32 /s /i:user "%~dp0x64\Debug\MyCom.dll"

if %ERRORLEVEL% EQU 0 (
    echo.
    echo Registration successful!
    echo The Office Add-in should now be available in Word, Excel, and PowerPoint.
    echo.
    echo To verify:
    echo 1. Open Word, Excel, or PowerPoint
    echo 2. Go to File ^> Options ^> Add-ins
    echo 3. Look for "MyCom Office Add-in" in the list
    echo.
    echo Note: This is a user-level registration, so the add-in will only be
    echo available for the current user.
    echo.
) else (
    echo.
    echo Registration failed with error code %ERRORLEVEL%!
    echo.
    echo Common error codes:
    echo 0x80070005 = Access denied (try running as Administrator)
    echo 0x80004005 = Unspecified error
    echo 0x80040200 = Invalid class string
    echo.
)

pause
