@echo off
echo ========================================
echo MyCom Office Add-in Registration
echo ========================================
echo.
echo This script will register the Office Add-in using PowerShell.
echo The registration will be done at user level (no admin rights required).
echo.

REM Check if PowerShell script exists
if not exist "%~dp0RegisterAddin.ps1" (
    echo Error: RegisterAddin.ps1 not found!
    echo Please make sure the PowerShell script is in the same directory.
    pause
    exit /b 1
)

REM Check if DLL exists
if not exist "%~dp0x64\Debug\MyCom.dll" (
    echo Error: MyCom.dll not found!
    echo Please build the project first using Visual Studio.
    echo Expected location: %~dp0x64\Debug\MyCom.dll
    pause
    exit /b 1
)

echo Running PowerShell registration script...
echo.
powershell -ExecutionPolicy Bypass -File "%~dp0RegisterAddin.ps1"

echo.
echo ========================================
echo Registration process completed.
echo ========================================
pause
