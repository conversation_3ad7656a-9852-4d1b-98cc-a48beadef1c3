@echo off
echo ========================================
echo MyCom Office Add-in Log Checker
echo ========================================
echo.

REM Check if PowerShell script exists
if not exist "%~dp0CheckLogs.ps1" (
    echo Error: CheckLogs.ps1 not found!
    echo Please make sure the PowerShell script is in the same directory.
    pause
    exit /b 1
)

echo Running PowerShell log checker...
echo.
powershell -ExecutionPolicy Bypass -File "%~dp0CheckLogs.ps1"

echo.
echo ========================================
echo Log check completed.
echo ========================================
echo.
echo If no logs are found, try:
echo 1. Close all Office applications
echo 2. Open Word, Excel, or PowerPoint
echo 3. Run this script again
echo.
pause
