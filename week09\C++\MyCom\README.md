# MyCom Office Add-in

一个可以在 Word、Excel 和 PowerPoint 中加载的 C++ Office 插件基础框架。

## 项目概述

这是一个使用 C++ 和 ATL (Active Template Library) 开发的 Office COM 插件，可以同时在以下 Office 应用程序中运行：

- **Microsoft Word** - 提供文档统计和格式化功能
- **Microsoft Excel** - 提供工作表统计和图表生成功能  
- **Microsoft PowerPoint** - 提供演示文稿统计和幻灯片管理功能

## 功能特性

### 当前实现的功能
- ✅ COM 组件基础框架
- ✅ Office 应用程序检测
- ✅ 插件连接和断开连接事件处理
- ✅ 用户级注册（无需管理员权限）
- ✅ 插件加载成功提示

### 计划实现的功能

#### Word 功能
- [ ] 自动统计中英文字数
- [ ] 格式化按钮（首行缩进2字符，1.5倍行距）

#### Excel 功能  
- [ ] 自动统计非空单元格数量
- [ ] 生成图表按钮

#### PowerPoint 功能
- [ ] 自动统计非空文本框数量
- [ ] 插入新幻灯片并设置主题色

## 编译要求

- Visual Studio 2022 或更高版本
- Windows 10/11
- Microsoft Office (Word, Excel, PowerPoint)
- ATL 支持

## 编译步骤

1. 打开 `MyCom.sln` 解决方案文件
2. 选择 `Debug` 或 `Release` 配置
3. 选择 `x64` 平台
4. 按 `F7` 或选择 `生成 > 生成解决方案`

编译成功后，DLL 文件将生成在 `x64\Debug\` 或 `x64\Release\` 目录中。

## 安装和注册

### 自动注册（推荐）

运行 PowerShell 脚本进行用户级注册：

```powershell
# 注册插件
.\RegisterAddin.ps1

# 测试注册状态
.\TestAddin.ps1

# 卸载插件
.\UnregisterAddin.ps1
```

### 手动注册

如果需要手动注册，可以使用以下命令：

```cmd
# 用户级注册
regsvr32 /i:user x64\Debug\MyCom.dll

# 系统级注册（需要管理员权限）
regsvr32 x64\Debug\MyCom.dll
```

## 验证安装

1. 运行 `TestAddin.ps1` 脚本检查注册状态
2. 打开 Word、Excel 或 PowerPoint
3. 插件加载成功时会显示消息框
4. 在 `文件 > 选项 > 加载项` 中查看插件列表

## 项目结构

```
MyCom/
├── MyCom.idl           # COM 接口定义
├── compreg.h           # Office 插件类声明
├── compreg.cpp         # Office 插件类实现
├── MyCom.cpp           # DLL 导出函数
├── dllmain.cpp         # DLL 主模块
├── OfficeAddin.rgs     # 注册表脚本
├── MyCom.rc            # 资源文件
├── RegisterAddin.ps1   # 注册脚本
├── UnregisterAddin.ps1 # 卸载脚本
├── TestAddin.ps1       # 测试脚本
└── README.md           # 说明文档
```

## 开发说明

### 主要类和接口

- `IOfficeAddin` - Office 插件接口，定义了插件生命周期方法
- `COfficeAddin` - Office 插件实现类，处理 Office 应用程序事件
- `CMyComModule` - ATL 模块类，管理 COM 组件

### 关键方法

- `OnConnection()` - 插件连接到 Office 应用程序时调用
- `OnDisconnection()` - 插件从 Office 应用程序断开时调用
- `DetectOfficeApplication()` - 检测当前 Office 应用程序类型
- `SetupWordFeatures()` - 设置 Word 特定功能
- `SetupExcelFeatures()` - 设置 Excel 特定功能  
- `SetupPowerPointFeatures()` - 设置 PowerPoint 特定功能

## 故障排除

### 常见问题

1. **注册失败 (错误代码 0x80070005)**
   - 确保以管理员身份运行，或使用用户级注册
   - 使用 `RegisterAddin.ps1` 脚本进行用户级注册

2. **插件不显示在 Office 中**
   - 运行 `TestAddin.ps1` 检查注册状态
   - 重启 Office 应用程序
   - 检查 Office 安全设置

3. **编译错误**
   - 确保安装了 ATL 开发组件
   - 检查 Windows SDK 版本
   - 确保项目配置正确

### 调试

1. 在 Visual Studio 中设置断点
2. 附加到 Office 进程进行调试
3. 查看 Windows 事件日志中的错误信息

## 许可证

本项目仅用于学习和演示目的。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。
