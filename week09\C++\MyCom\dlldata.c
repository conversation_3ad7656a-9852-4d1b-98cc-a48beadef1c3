/*********************************************************
   DllData file -- generated by MIDL compiler 

        DO NOT ALTER THIS FILE

   This file is regenerated by <PERSON><PERSON><PERSON> on every IDL file compile.

   To completely reconstruct this file, delete it and rerun MIDL
   on all the IDL files in this DLL, specifying this file for the
   /dlldata command line option

*********************************************************/

#define PROXY_DELEGATION

#include <rpcproxy.h>

#ifdef __cplusplus
extern "C"   {
#endif

EXTERN_PROXY_FILE( MyCom )


PROXYFILE_LIST_START
/* Start of list */
  REFERENCE_PROXY_FILE( MyCom ),
/* End of list */
PROXYFILE_LIST_END


DLLDATA_ROUTINES( a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ist, GET_DLL_CLSID )

#ifdef __cplusplus
}  /*extern "C" */
#endif

/* end of generated dlldata file */
