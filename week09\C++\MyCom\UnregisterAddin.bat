@echo off
echo ========================================
echo MyCom Office Add-in Unregistration
echo ========================================
echo.
echo This script will unregister the Office Add-in using PowerShell.
echo.

REM Check if PowerShell script exists
if not exist "%~dp0UnregisterAddin.ps1" (
    echo Error: UnregisterAddin.ps1 not found!
    echo Please make sure the PowerShell script is in the same directory.
    pause
    exit /b 1
)

echo Running PowerShell unregistration script...
echo.
powershell -ExecutionPolicy Bypass -File "%~dp0UnregisterAddin.ps1"

echo.
echo ========================================
echo Unregistration process completed.
echo ========================================
pause
