# PowerShell script to test COM component instantiation

Write-Host "Testing MyCom COM Component..." -ForegroundColor Green
Write-Host ""

try {
    # Test COM component creation
    $progId = "MyCom.OfficeAddin"
    
    Write-Host "Attempting to create COM object with ProgID: $progId" -ForegroundColor Yellow
    
    $comObject = New-Object -ComObject $progId
    
    if ($comObject) {
        Write-Host "✅ COM object created successfully!" -ForegroundColor Green
        Write-Host "Object type: $($comObject.GetType().FullName)" -ForegroundColor Cyan
        
        # Try to call a method if available
        try {
            # Release the object
            [System.Runtime.InteropServices.Marshal]::ReleaseComObject($comObject) | Out-Null
            Write-Host "✅ COM object released successfully!" -ForegroundColor Green
        } catch {
            Write-Host "⚠️  Warning: Could not release COM object: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    } else {
        Write-Host "❌ Failed to create COM object" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error creating COM object:" -ForegroundColor Red
    Write-Host "   $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    
    # Check if the error is related to class not registered
    if ($_.Exception.Message -like "*0x80040154*" -or $_.Exception.Message -like "*class not registered*") {
        Write-Host "This error indicates the COM component is not properly registered." -ForegroundColor Yellow
        Write-Host "Try running RegisterAddin.ps1 again." -ForegroundColor Yellow
    }
    elseif ($_.Exception.Message -like "*0x80070005*") {
        Write-Host "This error indicates access denied." -ForegroundColor Yellow
        Write-Host "Try running as Administrator." -ForegroundColor Yellow
    }
    elseif ($_.Exception.Message -like "*0x8007007E*") {
        Write-Host "This error indicates the DLL could not be loaded." -ForegroundColor Yellow
        Write-Host "Check if all required dependencies are available." -ForegroundColor Yellow
    }
    else {
        Write-Host "Unknown error. Check Windows Event Log for more details." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "Additional checks:" -ForegroundColor Cyan

# Check if DLL exists
$dllPath = Join-Path $PSScriptRoot "x64\Release\MyCom.dll"
if (-not (Test-Path $dllPath)) {
    $dllPath = Join-Path $PSScriptRoot "x64\Debug\MyCom.dll"
}

if (Test-Path $dllPath) {
    Write-Host "✅ DLL file exists: $dllPath" -ForegroundColor Green
    
    # Check file size
    $fileInfo = Get-Item $dllPath
    Write-Host "   File size: $($fileInfo.Length) bytes" -ForegroundColor Cyan
    Write-Host "   Modified: $($fileInfo.LastWriteTime)" -ForegroundColor Cyan
} else {
    Write-Host "❌ DLL file not found" -ForegroundColor Red
}

# Check registry entries
$clsid = "{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}"
$clsidPath = "HKCU:\Software\Classes\CLSID\$clsid"

if (Test-Path $clsidPath) {
    Write-Host "✅ CLSID registry entry exists" -ForegroundColor Green
    
    $inprocPath = "$clsidPath\InprocServer32"
    if (Test-Path $inprocPath) {
        $dllRegPath = Get-ItemProperty -Path $inprocPath -Name "(Default)" -ErrorAction SilentlyContinue
        if ($dllRegPath) {
            Write-Host "   Registered DLL: $($dllRegPath.'(Default)')" -ForegroundColor Cyan
            
            if (Test-Path $dllRegPath.'(Default)') {
                Write-Host "   ✅ Registered DLL file exists" -ForegroundColor Green
            } else {
                Write-Host "   ❌ Registered DLL file not found!" -ForegroundColor Red
            }
        }
    }
} else {
    Write-Host "❌ CLSID registry entry not found" -ForegroundColor Red
}
