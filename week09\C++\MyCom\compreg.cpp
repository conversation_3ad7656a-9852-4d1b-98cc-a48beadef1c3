// compreg.cpp : Implementation of COfficeAddin

#include "pch.h"
#include "framework.h"
#include "compreg.h"
#include <comdef.h>
#include <fstream>
#include <ctime>

// Simple logging function
void LogMessage(const wchar_t* message)
{
	std::wofstream logFile(L"C:\\temp\\MyCom_debug.log", std::ios::app);
	if (logFile.is_open())
	{
		time_t now = time(0);
		wchar_t timeStr[100];
		struct tm timeinfo;
		localtime_s(&timeinfo, &now);
		wcsftime(timeStr, sizeof(timeStr)/sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);

		logFile << timeStr << L" - " << message << std::endl;
		logFile.close();
	}
}

// COfficeAddin

STDMETHODIMP COfficeAddin::OnConnection(IDispatch* Application, long ConnectMode, IDispatch* AddInInst, SAFEARRAY** custom)
{
	LogMessage(L"OnConnection called!");

	HRESULT hr = S_OK;

	try
	{
		// Store references to the application and add-in instance
		if (Application)
		{
			m_pApplication = Application;
			m_pApplication->AddRef();
		}

		if (AddInInst)
		{
			m_pAddInInstance = AddInInst;
			m_pAddInInstance->AddRef();
		}

		// Detect which Office application we're connecting to and setup features
		hr = DetectOfficeApplication();

		// Show a simple message to indicate the add-in has loaded
		wchar_t message[512];
		swprintf_s(message, L"MyCom Office Add-in Connected Successfully!\n\nConnect Mode: %ld\nApplication: %p\nAdd-in Instance: %p",
			ConnectMode, Application, AddInInst);
		MessageBox(NULL, message, L"MyCom Add-in - DEBUG", MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
	}
	catch (...)
	{
		hr = E_FAIL;
	}

	return hr;
}

STDMETHODIMP COfficeAddin::OnDisconnection(long RemoveMode, SAFEARRAY** custom)
{
	// Clean up resources
	if (m_pApplication)
	{
		m_pApplication->Release();
		m_pApplication = nullptr;
	}

	if (m_pAddInInstance)
	{
		m_pAddInInstance->Release();
		m_pAddInInstance = nullptr;
	}

	return S_OK;
}

STDMETHODIMP COfficeAddin::OnAddInsUpdate(SAFEARRAY** custom)
{
	return S_OK;
}

STDMETHODIMP COfficeAddin::OnStartupComplete(SAFEARRAY** custom)
{
	return S_OK;
}

STDMETHODIMP COfficeAddin::OnBeginShutdown(SAFEARRAY** custom)
{
	return S_OK;
}

HRESULT COfficeAddin::DetectOfficeApplication()
{
	if (!m_pApplication)
		return E_FAIL;

	HRESULT hr = S_OK;
	_bstr_t appName;

	try
	{
		// Get the application name to determine which Office app we're in
		DISPID dispid;
		OLECHAR* szMember = L"Name";
		hr = m_pApplication->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);

		if (SUCCEEDED(hr))
		{
			DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
			VARIANT result;
			VariantInit(&result);

			hr = m_pApplication->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);

			if (SUCCEEDED(hr) && result.vt == VT_BSTR)
			{
				appName = result.bstrVal;

				// Setup features based on the application
				const wchar_t* appNameStr = static_cast<const wchar_t*>(appName);
				if (wcsstr(appNameStr, L"Word") || wcsstr(appNameStr, L"Microsoft Word"))
				{
					hr = SetupWordFeatures();
				}
				else if (wcsstr(appNameStr, L"Excel") || wcsstr(appNameStr, L"Microsoft Excel"))
				{
					hr = SetupExcelFeatures();
				}
				else if (wcsstr(appNameStr, L"PowerPoint") || wcsstr(appNameStr, L"Microsoft PowerPoint"))
				{
					hr = SetupPowerPointFeatures();
				}
			}

			VariantClear(&result);
		}
	}
	catch (...)
	{
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::SetupWordFeatures()
{
	// TODO: Implement Word-specific features
	// - Document statistics (character count)
	// - Format button for paragraph formatting
	return S_OK;
}

HRESULT COfficeAddin::SetupExcelFeatures()
{
	// TODO: Implement Excel-specific features
	// - Cell count statistics
	// - Chart generation button
	return S_OK;
}

HRESULT COfficeAddin::SetupPowerPointFeatures()
{
	// TODO: Implement PowerPoint-specific features
	// - Text box count statistics
	// - Slide insertion with theme
	return S_OK;
}
