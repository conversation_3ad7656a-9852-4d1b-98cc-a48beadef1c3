// compreg.cpp : Implementation of COfficeAddin

#include "pch.h"
#include "framework.h"
#include "compreg.h"
#include <comdef.h>
#include <fstream>
#include <ctime>

// Simple logging function
void LogMessage(const wchar_t* message)
{
	std::wofstream logFile(L"C:\\temp\\MyCom_debug.log", std::ios::app);
	if (logFile.is_open())
	{
		time_t now = time(0);
		wchar_t timeStr[100];
		struct tm timeinfo;
		localtime_s(&timeinfo, &now);
		wcsftime(timeStr, sizeof(timeStr)/sizeof(wchar_t), L"%Y-%m-%d %H:%M:%S", &timeinfo);

		logFile << timeStr << L" - " << message << std::endl;
		logFile.close();
	}
}

// COfficeAddin

STDMETHODIMP COfficeAddin::OnConnection(IDispatch* Application, long ConnectMode, IDispatch* AddInInst, SAFEARRAY** custom)
{
	LogMessage(L"OnConnection called!");

	HRESULT hr = S_OK;

	try
	{
		// Store references to the application and add-in instance
		if (Application)
		{
			m_pApplication = Application;
			m_pApplication->AddRef();
		}

		if (AddInInst)
		{
			m_pAddInInstance = AddInInst;
			m_pAddInInstance->AddRef();
		}

		// Show a simple message to indicate the add-in has loaded
		MessageBox(NULL, L"MyCom Office Add-in Connected Successfully!", L"MyCom Add-in", MB_OK | MB_ICONINFORMATION | MB_TOPMOST);

		// Detect which Office application we're connecting to and setup features
		hr = DetectOfficeApplication();
		if (FAILED(hr))
		{
			LogMessage(L"Failed to detect Office application, but continuing...");
			hr = S_OK; // Don't fail the connection for this
		}
	}
	catch (...)
	{
		LogMessage(L"Exception in OnConnection");
		hr = E_FAIL;
	}

	return hr;
}

STDMETHODIMP COfficeAddin::OnDisconnection(long RemoveMode, SAFEARRAY** custom)
{
	// Clean up resources
	if (m_pApplication)
	{
		m_pApplication->Release();
		m_pApplication = nullptr;
	}

	if (m_pAddInInstance)
	{
		m_pAddInInstance->Release();
		m_pAddInInstance = nullptr;
	}

	return S_OK;
}

STDMETHODIMP COfficeAddin::OnAddInsUpdate(SAFEARRAY** custom)
{
	return S_OK;
}

STDMETHODIMP COfficeAddin::OnStartupComplete(SAFEARRAY** custom)
{
	return S_OK;
}

STDMETHODIMP COfficeAddin::OnBeginShutdown(SAFEARRAY** custom)
{
	return S_OK;
}

HRESULT COfficeAddin::DetectOfficeApplication()
{
	if (!m_pApplication)
		return E_FAIL;

	HRESULT hr = S_OK;

	try
	{
		LogMessage(L"Detecting Office application...");

		// Get the application name to determine which Office app we're in
		DISPID dispid;
		OLECHAR* szMember = L"Name";
		hr = m_pApplication->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);

		if (SUCCEEDED(hr))
		{
			DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
			VARIANT result;
			VariantInit(&result);

			hr = m_pApplication->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);

			if (SUCCEEDED(hr) && result.vt == VT_BSTR)
			{
				_bstr_t appName = result.bstrVal;
				const wchar_t* appNameStr = static_cast<const wchar_t*>(appName);

				wchar_t logMsg[256];
				swprintf_s(logMsg, L"Detected application: %s", appNameStr);
				LogMessage(logMsg);

				// Setup features based on the application
				if (wcsstr(appNameStr, L"Word") || wcsstr(appNameStr, L"Microsoft Word"))
				{
					LogMessage(L"Setting up Word features...");
					hr = SetupWordFeatures();
				}
				else if (wcsstr(appNameStr, L"Excel") || wcsstr(appNameStr, L"Microsoft Excel"))
				{
					LogMessage(L"Setting up Excel features...");
					hr = SetupExcelFeatures();
				}
				else if (wcsstr(appNameStr, L"PowerPoint") || wcsstr(appNameStr, L"Microsoft PowerPoint"))
				{
					LogMessage(L"Setting up PowerPoint features...");
					hr = SetupPowerPointFeatures();
				}
				else
				{
					LogMessage(L"Unknown Office application");
				}
			}
			else
			{
				LogMessage(L"Failed to get application name");
			}

			VariantClear(&result);
		}
		else
		{
			LogMessage(L"Failed to get Name property");
		}
	}
	catch (...)
	{
		LogMessage(L"Exception in DetectOfficeApplication");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::SetupWordFeatures()
{
	LogMessage(L"Setting up Word features...");

	HRESULT hr = S_OK;

	try
	{
		// Store Word application reference
		if (m_pApplication)
		{
			m_pWordApp = m_pApplication;
			m_pWordApp->AddRef();
		}

		// Create custom toolbar
		hr = CreateWordToolbar();
		if (FAILED(hr))
		{
			LogMessage(L"Failed to create Word toolbar");
		}

		// Show initial document statistics
		hr = ShowWordStatistics();
		if (FAILED(hr))
		{
			LogMessage(L"Failed to show Word statistics");
		}

		LogMessage(L"Word features setup completed");
	}
	catch (...)
	{
		LogMessage(L"Exception in SetupWordFeatures");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::SetupExcelFeatures()
{
	// TODO: Implement Excel-specific features
	// - Cell count statistics
	// - Chart generation button
	return S_OK;
}

HRESULT COfficeAddin::SetupPowerPointFeatures()
{
	// TODO: Implement PowerPoint-specific features
	// - Text box count statistics
	// - Slide insertion with theme
	return S_OK;
}

HRESULT COfficeAddin::CreateWordToolbar()
{
	LogMessage(L"Creating Word toolbar...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		// Get CommandBars collection
		DISPID dispid;
		OLECHAR* szMember = L"CommandBars";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
		{
			LogMessage(L"Failed to get CommandBars property");
			return hr;
		}

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			LogMessage(L"Failed to get CommandBars collection");
			VariantClear(&result);
			return hr;
		}

		IDispatch* pCommandBars = result.pdispVal;

		// Add a new toolbar
		szMember = L"Add";
		hr = pCommandBars->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT args[1];
			VariantInit(&args[0]);
			args[0].vt = VT_BSTR;
			args[0].bstrVal = SysAllocString(L"MyCom Format Tools");

			DISPPARAMS addParams;
			addParams.rgvarg = args;
			addParams.cArgs = 1;
			addParams.rgdispidNamedArgs = NULL;
			addParams.cNamedArgs = 0;

			VARIANT toolbarResult;
			VariantInit(&toolbarResult);

			hr = pCommandBars->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_METHOD, &addParams, &toolbarResult, NULL, NULL);
			if (SUCCEEDED(hr) && toolbarResult.vt == VT_DISPATCH)
			{
				m_pCommandBar = toolbarResult.pdispVal;
				m_pCommandBar->AddRef();

				// Make toolbar visible
				szMember = L"Visible";
				hr = m_pCommandBar->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT visibleArg;
					VariantInit(&visibleArg);
					visibleArg.vt = VT_BOOL;
					visibleArg.boolVal = VARIANT_TRUE;

					DISPPARAMS visibleParams;
					visibleParams.rgvarg = &visibleArg;
					visibleParams.cArgs = 1;
					visibleParams.rgdispidNamedArgs = NULL;
					visibleParams.cNamedArgs = 0;

					m_pCommandBar->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &visibleParams, NULL, NULL, NULL);
				}

				LogMessage(L"Word toolbar created successfully");
			}

			VariantClear(&toolbarResult);
			VariantClear(&args[0]);
		}

		pCommandBars->Release();
		VariantClear(&result);
	}
	catch (...)
	{
		LogMessage(L"Exception in CreateWordToolbar");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::ShowWordStatistics()
{
	LogMessage(L"Showing Word statistics...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		BSTR documentText = nullptr;
		hr = GetDocumentText(&documentText);

		if (SUCCEEDED(hr) && documentText)
		{
			int chineseCount = 0;
			int englishWords = 0;

			hr = CountChineseAndEnglishWords(documentText, chineseCount, englishWords);

			if (SUCCEEDED(hr))
			{
				wchar_t statsMessage[512];
				swprintf_s(statsMessage, 512,
					L"Document Statistics\n\nChinese Characters: %d\nEnglish Words: %d\nTotal Characters: %d\n\nStatistics Complete!",
					chineseCount, englishWords, (int)wcslen(documentText));

				MessageBox(NULL, statsMessage, L"MyCom - Word Document Statistics", MB_OK | MB_ICONINFORMATION | MB_TOPMOST);

				wchar_t logMsg[256];
				swprintf_s(logMsg, L"Word statistics: Chinese=%d, English=%d, Total=%d",
					chineseCount, englishWords, (int)wcslen(documentText));
				LogMessage(logMsg);
			}

			SysFreeString(documentText);
		}
		else
		{
			LogMessage(L"Failed to get document text");
		}
	}
	catch (...)
	{
		LogMessage(L"Exception in ShowWordStatistics");
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::GetDocumentText(BSTR* documentText)
{
	if (!documentText)
		return E_INVALIDARG;

	*documentText = nullptr;
	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		// Get ActiveDocument
		DISPID dispid;
		OLECHAR* szMember = L"ActiveDocument";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
			return hr;

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			VariantClear(&result);
			return hr;
		}

		IDispatch* pDocument = result.pdispVal;

		// Get Range property
		szMember = L"Range";
		hr = pDocument->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			hr = pDocument->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
			if (SUCCEEDED(hr) && result.vt == VT_DISPATCH)
			{
				IDispatch* pRange = result.pdispVal;

				// Get Text property
				szMember = L"Text";
				hr = pRange->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT textResult;
					VariantInit(&textResult);

					hr = pRange->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &textResult, NULL, NULL);
					if (SUCCEEDED(hr) && textResult.vt == VT_BSTR)
					{
						*documentText = SysAllocString(textResult.bstrVal);
					}

					VariantClear(&textResult);
				}

				pRange->Release();
			}
		}

		pDocument->Release();
		VariantClear(&result);
	}
	catch (...)
	{
		hr = E_FAIL;
	}

	return hr;
}

HRESULT COfficeAddin::CountChineseAndEnglishWords(const wchar_t* text, int& chineseCount, int& englishWords)
{
	if (!text)
		return E_INVALIDARG;

	chineseCount = 0;
	englishWords = 0;

	try
	{
		int len = (int)wcslen(text);
		bool inEnglishWord = false;

		for (int i = 0; i < len; i++)
		{
			wchar_t ch = text[i];

			// Check if character is Chinese (CJK Unified Ideographs)
			if ((ch >= 0x4E00 && ch <= 0x9FFF) ||  // CJK Unified Ideographs
				(ch >= 0x3400 && ch <= 0x4DBF) ||  // CJK Extension A
				(ch >= 0x20000 && ch <= 0x2A6DF) || // CJK Extension B
				(ch >= 0x2A700 && ch <= 0x2B73F) || // CJK Extension C
				(ch >= 0x2B740 && ch <= 0x2B81F) || // CJK Extension D
				(ch >= 0x2B820 && ch <= 0x2CEAF))   // CJK Extension E
			{
				chineseCount++;
				inEnglishWord = false;
			}
			// Check if character is English letter
			else if ((ch >= L'A' && ch <= L'Z') || (ch >= L'a' && ch <= L'z'))
			{
				if (!inEnglishWord)
				{
					englishWords++;
					inEnglishWord = true;
				}
			}
			// Check if character is digit (count as part of English word)
			else if (ch >= L'0' && ch <= L'9')
			{
				if (!inEnglishWord)
				{
					englishWords++;
					inEnglishWord = true;
				}
			}
			// Other characters (spaces, punctuation, etc.)
			else
			{
				inEnglishWord = false;
			}
		}
	}
	catch (...)
	{
		return E_FAIL;
	}

	return S_OK;
}

HRESULT COfficeAddin::FormatSelectedText()
{
	LogMessage(L"Formatting selected text...");

	HRESULT hr = S_OK;

	try
	{
		if (!m_pWordApp)
			return E_FAIL;

		// Get Selection object
		DISPID dispid;
		OLECHAR* szMember = L"Selection";
		hr = m_pWordApp->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (FAILED(hr))
			return hr;

		DISPPARAMS dispparams = { NULL, NULL, 0, 0 };
		VARIANT result;
		VariantInit(&result);

		hr = m_pWordApp->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &result, NULL, NULL);
		if (FAILED(hr) || result.vt != VT_DISPATCH)
		{
			VariantClear(&result);
			return hr;
		}

		IDispatch* pSelection = result.pdispVal;

		// Get ParagraphFormat
		szMember = L"ParagraphFormat";
		hr = pSelection->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
		if (SUCCEEDED(hr))
		{
			VARIANT paraResult;
			VariantInit(&paraResult);

			hr = pSelection->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYGET, &dispparams, &paraResult, NULL, NULL);
			if (SUCCEEDED(hr) && paraResult.vt == VT_DISPATCH)
			{
				IDispatch* pParagraphFormat = paraResult.pdispVal;

				// Set FirstLineIndent to 2 characters (approximately 24 points)
				szMember = L"FirstLineIndent";
				hr = pParagraphFormat->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT indentArg;
					VariantInit(&indentArg);
					indentArg.vt = VT_R4;
					indentArg.fltVal = 24.0f; // 2 characters worth of indent

					DISPPARAMS indentParams;
					indentParams.rgvarg = &indentArg;
					indentParams.cArgs = 1;
					indentParams.rgdispidNamedArgs = NULL;
					indentParams.cNamedArgs = 0;

					pParagraphFormat->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &indentParams, NULL, NULL, NULL);
				}

				// Set LineSpacing to 1.5
				szMember = L"LineSpacing";
				hr = pParagraphFormat->GetIDsOfNames(IID_NULL, &szMember, 1, LOCALE_USER_DEFAULT, &dispid);
				if (SUCCEEDED(hr))
				{
					VARIANT spacingArg;
					VariantInit(&spacingArg);
					spacingArg.vt = VT_R4;
					spacingArg.fltVal = 18.0f; // 1.5 line spacing (12pt * 1.5)

					DISPPARAMS spacingParams;
					spacingParams.rgvarg = &spacingArg;
					spacingParams.cArgs = 1;
					spacingParams.rgdispidNamedArgs = NULL;
					spacingParams.cNamedArgs = 0;

					pParagraphFormat->Invoke(dispid, IID_NULL, LOCALE_USER_DEFAULT, DISPATCH_PROPERTYPUT, &spacingParams, NULL, NULL, NULL);
				}

				pParagraphFormat->Release();
				LogMessage(L"Text formatting applied successfully");

				MessageBox(NULL,
					L"Formatting Complete!\n\nApplied Format:\n- First Line Indent: 2 characters\n- Line Spacing: 1.5x\n\nFormat applied to selected text.",
					L"MyCom - Formatting Complete",
					MB_OK | MB_ICONINFORMATION | MB_TOPMOST);
			}

			VariantClear(&paraResult);
		}

		pSelection->Release();
		VariantClear(&result);
	}
	catch (...)
	{
		LogMessage(L"Exception in FormatSelectedText");
		hr = E_FAIL;
	}

	return hr;
}
