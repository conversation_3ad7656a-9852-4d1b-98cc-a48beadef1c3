# MyCom Office Add-in 测试指南

## 🎯 当前实现的功能

### ✅ 已完成
1. **基础 COM 组件框架** - 可以正确注册和实例化
2. **Office 应用程序检测** - 自动识别 Word、Excel、PowerPoint
3. **Word 功能基础** - 连接成功提示和应用程序检测
4. **用户级注册** - 无需管理员权限
5. **完整的日志记录** - 调试和故障排除

### 🚧 正在开发
1. **Word 字数统计** - 中英文字符统计算法已实现
2. **Word 格式化功能** - 首行缩进和行距设置
3. **Word 自定义工具栏** - 格式化按钮

## 📋 测试步骤

### 1. 编译项目
```bash
# 编译 Release 版本（推荐）
msbuild MyCom.sln /p:Configuration=Release /p:Platform=x64

# 或编译 Debug 版本
msbuild MyCom.sln /p:Configuration=Debug /p:Platform=x64
```

### 2. 注册插件
```powershell
# 注册插件
.\RegisterAddin.ps1

# 验证注册
.\TestAddin.ps1

# 测试 COM 组件
.\TestCOM.ps1
```

### 3. 测试 Office 集成

#### Word 测试
1. **打开 Word**
   - 启动 Microsoft Word
   - 观察是否出现连接成功的消息框

2. **检查插件状态**
   - 转到 `文件 > 选项 > 加载项`
   - 查找 "MyCom Office Add-in for Word"
   - 确认状态为"已加载"

3. **查看日志**
   ```powershell
   .\CheckLogs.ps1
   ```
   应该看到类似以下的日志条目：
   ```
   2025-06-22 22:55:00 - OnConnection called!
   2025-06-22 22:55:00 - Detecting Office application...
   2025-06-22 22:55:00 - Detected application: Microsoft Word
   2025-06-22 22:55:00 - Setting up Word features...
   2025-06-22 22:55:00 - Word features setup completed
   ```

#### Excel 测试
1. **打开 Excel**
   - 启动 Microsoft Excel
   - 观察是否出现连接成功的消息框

2. **检查日志**
   - 应该看到 "Setting up Excel features..." 的日志

#### PowerPoint 测试
1. **打开 PowerPoint**
   - 启动 Microsoft PowerPoint
   - 观察是否出现连接成功的消息框

2. **检查日志**
   - 应该看到 "Setting up PowerPoint features..." 的日志

## 🔧 故障排除

### 问题 1: "加载COM项时出现错误"
**解决方案:**
1. 确保使用 Release 版本的 DLL
2. 关闭所有 Office 应用程序
3. 重新注册插件
4. 检查依赖项是否完整

### 问题 2: 插件不显示在 Office 中
**解决方案:**
1. 运行 `TestAddin.ps1` 检查注册状态
2. 确保 LoadBehavior = 3
3. 重启 Office 应用程序
4. 检查 Office 安全设置

### 问题 3: 没有消息框出现
**解决方案:**
1. 检查日志文件 `C:\temp\MyCom_debug.log`
2. 确认 OnConnection 是否被调用
3. 检查消息框是否被阻止

### 问题 4: COM 组件创建失败
**解决方案:**
1. 运行 `TestCOM.ps1` 诊断
2. 检查注册表项是否正确
3. 验证 DLL 文件路径

## 📊 预期结果

### 成功的测试应该显示：

1. **注册测试** (`TestAddin.ps1`)
   ```
   ✓ CLSID registration found
   ✓ Versioned ProgID registration found
   ✓ Version-independent ProgID registration found
   ✓ Word add-in registration found
     LoadBehavior: 3
   ✓ Excel add-in registration found
     LoadBehavior: 3
   ✓ PowerPoint add-in registration found
     LoadBehavior: 3
   ```

2. **COM 测试** (`TestCOM.ps1`)
   ```
   ✓ COM object created successfully!
   ✓ COM object released successfully!
   ✓ DLL file exists
   ✓ CLSID registry entry exists
   ✓ Registered DLL file exists
   ```

3. **Office 启动**
   - 消息框: "MyCom Office Add-in Connected Successfully!"
   - 对于 Word: 额外消息框显示 "Word Add-in Loaded!"

4. **日志文件** (`C:\temp\MyCom_debug.log`)
   ```
   2025-06-22 22:55:00 - OnConnection called!
   2025-06-22 22:55:00 - Detecting Office application...
   2025-06-22 22:55:00 - Detected application: Microsoft Word
   2025-06-22 22:55:00 - Setting up Word features...
   2025-06-22 22:55:00 - Word features setup completed
   ```

## 🚀 下一步开发

1. **完善 Word 功能**
   - 实现完整的字数统计功能
   - 添加自定义工具栏和格式化按钮
   - 处理文档事件

2. **实现 Excel 功能**
   - 单元格计数统计
   - 图表生成功能

3. **实现 PowerPoint 功能**
   - 文本框统计
   - 幻灯片插入功能

## 📞 支持

如果遇到问题，请：
1. 运行所有测试脚本收集信息
2. 检查日志文件
3. 提供错误截图和详细描述
