# PowerShell script to test Word features manually

Write-Host "Testing MyCom Word Features..." -ForegroundColor Green
Write-Host ""

try {
    # Create COM object
    $progId = "MyCom.OfficeAddin"
    Write-Host "Creating COM object..." -ForegroundColor Yellow
    $comObject = New-Object -ComObject $progId
    
    if ($comObject) {
        Write-Host "✅ COM object created successfully!" -ForegroundColor Green
        
        # Try to start Word
        Write-Host "Starting Word application..." -ForegroundColor Yellow
        $wordApp = New-Object -ComObject Word.Application
        $wordApp.Visible = $true
        
        # Create a new document with sample text
        Write-Host "Creating test document..." -ForegroundColor Yellow
        $doc = $wordApp.Documents.Add()
        
        # Add sample text with Chinese and English
        $sampleText = "This is a sample document for testing the MyCom Office Add-in.`n`n" +
                     "这是一个测试文档，用于验证MyCom Office插件的功能。`n`n" +
                     "Features to test:`n" +
                     "1. Chinese character counting: 中文字符统计`n" +
                     "2. English word counting`n" +
                     "3. Text formatting with first line indent and line spacing`n`n" +
                     "测试功能：`n" +
                     "1. 中文字符计数`n" +
                     "2. 英文单词计数`n" +
                     "3. 文本格式化（首行缩进和行距）`n`n" +
                     "The add-in should automatically detect this document and provide statistics.`n" +
                     "插件应该自动检测此文档并提供统计信息。"
        
        $doc.Content.Text = $sampleText
        
        Write-Host "✅ Test document created with sample text!" -ForegroundColor Green
        Write-Host ""
        Write-Host "Sample text includes:" -ForegroundColor Cyan
        Write-Host "- English words and sentences" -ForegroundColor White
        Write-Host "- Chinese characters and sentences" -ForegroundColor White
        Write-Host "- Mixed content for testing" -ForegroundColor White
        Write-Host ""
        
        # Simulate add-in connection
        Write-Host "Simulating add-in connection..." -ForegroundColor Yellow
        try {
            # Call OnConnection method
            $comObject.OnConnection($wordApp, 0, $null, $null)
            Write-Host "✅ OnConnection called successfully!" -ForegroundColor Green

            # Wait a moment for initialization
            Start-Sleep -Seconds 2

            # Manually trigger Word features for the document
            Write-Host "Triggering Word features for the document..." -ForegroundColor Yellow
            $comObject.TriggerWordFeatures()
            Write-Host "✅ TriggerWordFeatures called successfully!" -ForegroundColor Green

        } catch {
            Write-Host "⚠️  Add-in method failed: $($_.Exception.Message)" -ForegroundColor Yellow
        }
        
        Write-Host ""
        Write-Host "📋 Next steps:" -ForegroundColor Cyan
        Write-Host "1. Check if the add-in toolbar appears in Word" -ForegroundColor White
        Write-Host "2. Look for 'MyCom Tools' toolbar" -ForegroundColor White
        Write-Host "3. Try the 'Word Statistics' button" -ForegroundColor White
        Write-Host "4. Try the 'Format Text' button" -ForegroundColor White
        Write-Host "5. Check the log file: C:\temp\MyCom_debug.log" -ForegroundColor White
        Write-Host ""
        Write-Host "Press any key to continue..." -ForegroundColor Yellow
        $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
        
        # Clean up COM objects
        [System.Runtime.InteropServices.Marshal]::ReleaseComObject($comObject) | Out-Null
        Write-Host "✅ Test completed!" -ForegroundColor Green
        
    } else {
        Write-Host "❌ Failed to create COM object" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ Error during testing:" -ForegroundColor Red
    Write-Host "   $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Troubleshooting:" -ForegroundColor Yellow
    Write-Host "1. Make sure the add-in is registered (run RegisterAddin.ps1)" -ForegroundColor White
    Write-Host "2. Check if Word is installed and accessible" -ForegroundColor White
    Write-Host "3. Run TestCOM.ps1 to verify COM registration" -ForegroundColor White
}

Write-Host ""
Write-Host "Testing completed." -ForegroundColor Green
