# PowerShell script to check MyCom Office Add-in logs

Write-Host "Checking MyCom Office Add-in logs..." -ForegroundColor Green
Write-Host ""

$logFile = "C:\temp\MyCom_debug.log"

if (Test-Path $logFile) {
    Write-Host "Log file found at: $logFile" -ForegroundColor Green
    Write-Host ""
    Write-Host "Recent log entries:" -ForegroundColor Yellow
    Write-Host "===================" -ForegroundColor Yellow
    
    $content = Get-Content $logFile -Tail 20
    if ($content) {
        foreach ($line in $content) {
            Write-Host $line -ForegroundColor White
        }
    } else {
        Write-Host "Log file is empty." -ForegroundColor Yellow
    }
} else {
    Write-Host "Log file not found at: $logFile" -ForegroundColor Red
    Write-Host ""
    Write-Host "This could mean:" -ForegroundColor Yellow
    Write-Host "1. The add-in has not been loaded by Office yet" -ForegroundColor White
    Write-Host "2. Office applications need to be restarted" -ForegroundColor White
    Write-Host "3. The add-in is not properly registered" -ForegroundColor White
    Write-Host "4. Office security settings are blocking the add-in" -ForegroundColor White
}

Write-Host ""
Write-Host "To test the add-in:" -ForegroundColor Cyan
Write-Host "1. Close all Office applications" -ForegroundColor White
Write-Host "2. Open Word, Excel, or PowerPoint" -ForegroundColor White
Write-Host "3. Check this log again" -ForegroundColor White
Write-Host "4. Look for message boxes when Office starts" -ForegroundColor White
