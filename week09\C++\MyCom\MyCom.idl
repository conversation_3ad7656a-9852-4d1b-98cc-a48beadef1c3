// MyCom.idl : IDL source for MyCom Office Add-in
//

// This file will be processed by the MIDL tool to
// produce the type library (MyCom.tlb) and marshalling code.

import "oaidl.idl";
import "ocidl.idl";

// Office Add-in interface (IDTExtensibility2)
[
	object,
	uuid(B65AD801-ABAF-11D0-BB8B-00A0C90F2744),
	dual,
	nonextensible,
	pointer_default(unique)
]
interface IDTExtensibility2 : IDispatch
{
	[id(1)] HRESULT OnConnection([in] IDispatch* Application, [in] long ConnectMode, [in] IDispatch* AddInInst, [in] SAFEARRAY(VARIANT)* custom);
	[id(2)] HRESULT OnDisconnection([in] long RemoveMode, [in] SAFEARRAY(VARIANT)* custom);
	[id(3)] HRESULT OnAddInsUpdate([in] SAFEARRAY(VARIANT)* custom);
	[id(4)] HRESULT OnStartupComplete([in] SAFEARRAY(VARIANT)* custom);
	[id(5)] HRESULT OnBeginShutdown([in] SAFEARRAY(VARIANT)* custom);
};

[
	uuid(43b6042f-c348-4de4-b8ab-77dac9fd1cb0),
	version(1.0),
	helpstring("MyCom Office Add-in 1.0 Type Library")
]
library MyComLib
{
	importlib("stdole2.tlb");

	[
		uuid(A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47),
		helpstring("Office Add-in Class")
	]
	coclass OfficeAddin
	{
		[default] interface IDTExtensibility2;
	};
};

