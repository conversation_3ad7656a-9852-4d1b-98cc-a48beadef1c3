// MyCom.idl : IDL source for MyCom
//

// This file will be processed by the MIDL tool to
// produce the type library (MyCom.tlb) and marshalling code.

import "oaidl.idl";
import "ocidl.idl";

[
	object,
	uuid(a817e7a2-43fa-11d0-9e44-00aa00b6770a),
	dual,
	pointer_default(unique)
]
interface IComponentRegistrar : IDispatch
{
	[id(1)]	HRESULT Attach([in] BSTR bstrPath);
	[id(2)]	HRESULT RegisterAll();
	[id(3)]	HRESULT UnregisterAll();
	[id(4)]	HRESULT GetComponents([out] SAFEARRAY(BSTR)* pbstrCLSIDs, [out] SAFEARRAY(BSTR)* pbstrDescriptions);
	[id(5)]	HRESULT RegisterComponent([in] BSTR bstrCLSID);
	[id(6)] HRESULT UnregisterComponent([in] BSTR bstrCLSID);
};

[
	uuid(43b6042f-c348-4de4-b8ab-77dac9fd1cb0),
	version(1.0),
	custom(a817e7a1-43fa-11d0-9e44-00aa00b6770a,"{07ee6099-687f-42c2-ad7f-67ab09527db5}")
]
library MyComLib
{
	importlib("stdole2.tlb");
	[
		uuid(07ee6099-687f-42c2-ad7f-67ab09527db5)
	]
	coclass CompReg
	{
		[default] interface IComponentRegistrar;
	};
};

