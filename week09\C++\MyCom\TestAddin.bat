@echo off
echo ========================================
echo MyCom Office Add-in Registration Test
echo ========================================
echo.
echo This script will test the Office Add-in registration status.
echo.

REM Check if PowerShell script exists
if not exist "%~dp0TestAddin.ps1" (
    echo Error: TestAddin.ps1 not found!
    echo Please make sure the PowerShell script is in the same directory.
    pause
    exit /b 1
)

echo Running PowerShell test script...
echo.
powershell -ExecutionPolicy Bypass -File "%~dp0TestAddin.ps1"

echo.
echo ========================================
echo Test completed.
echo ========================================
pause
