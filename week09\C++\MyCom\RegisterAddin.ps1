# PowerShell script to register MyCom Office Add-in
# This script registers the add-in for the current user only

Write-Host "Registering MyCom Office Add-in for current user..." -ForegroundColor Green
Write-Host ""

# Get the full path to the DLL
$dllPath = Join-Path $PSScriptRoot "x64\Debug\MyCom.dll"

if (-not (Test-Path $dllPath)) {
    Write-Host "Error: MyCom.dll not found at $dllPath" -ForegroundColor Red
    Write-Host "Please build the project first." -ForegroundColor Red
    exit 1
}

Write-Host "DLL Path: $dllPath" -ForegroundColor Yellow

try {
    # Register COM component in user registry
    $clsid = "{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}"
    $progId = "MyCom.OfficeAddin"
    $progIdVersioned = "MyCom.OfficeAddin.1"
    $typeLibId = "{43b6042f-c348-4de4-b8ab-77dac9fd1cb0}"
    
    # Create CLSID registry entries
    $clsidPath = "HKCU:\Software\Classes\CLSID\$clsid"
    New-Item -Path $clsidPath -Force | Out-Null
    Set-ItemProperty -Path $clsidPath -Name "(Default)" -Value "MyCom Office Add-in"
    
    # InprocServer32
    $inprocPath = "$clsidPath\InprocServer32"
    New-Item -Path $inprocPath -Force | Out-Null
    Set-ItemProperty -Path $inprocPath -Name "(Default)" -Value $dllPath
    Set-ItemProperty -Path $inprocPath -Name "ThreadingModel" -Value "Apartment"
    
    # TypeLib
    Set-ItemProperty -Path $clsidPath -Name "TypeLib" -Value $typeLibId
    Set-ItemProperty -Path $clsidPath -Name "Version" -Value "1.0"
    Set-ItemProperty -Path $clsidPath -Name "ProgID" -Value $progIdVersioned
    Set-ItemProperty -Path $clsidPath -Name "VersionIndependentProgID" -Value $progId
    
    # Create ProgID entries
    $progIdPath = "HKCU:\Software\Classes\$progIdVersioned"
    New-Item -Path $progIdPath -Force | Out-Null
    Set-ItemProperty -Path $progIdPath -Name "(Default)" -Value "MyCom Office Add-in"
    
    $progIdClsidPath = "$progIdPath\CLSID"
    New-Item -Path $progIdClsidPath -Force | Out-Null
    Set-ItemProperty -Path $progIdClsidPath -Name "(Default)" -Value $clsid
    
    $progIdViPath = "HKCU:\Software\Classes\$progId"
    New-Item -Path $progIdViPath -Force | Out-Null
    Set-ItemProperty -Path $progIdViPath -Name "(Default)" -Value "MyCom Office Add-in"
    
    $progIdViClsidPath = "$progIdViPath\CLSID"
    New-Item -Path $progIdViClsidPath -Force | Out-Null
    Set-ItemProperty -Path $progIdViClsidPath -Name "(Default)" -Value $clsid
    
    $progIdViCurVerPath = "$progIdViPath\CurVer"
    New-Item -Path $progIdViCurVerPath -Force | Out-Null
    Set-ItemProperty -Path $progIdViCurVerPath -Name "(Default)" -Value $progIdVersioned
    
    Write-Host "COM component registered successfully!" -ForegroundColor Green
    
    # Register Office Add-ins
    $officeApps = @(
        @{ Name = "Word"; FriendlyName = "MyCom Office Add-in for Word"; Description = "Provides document statistics and formatting features" },
        @{ Name = "Excel"; FriendlyName = "MyCom Office Add-in for Excel"; Description = "Provides worksheet statistics and chart generation features" },
        @{ Name = "PowerPoint"; FriendlyName = "MyCom Office Add-in for PowerPoint"; Description = "Provides presentation statistics and slide management features" }
    )
    
    foreach ($app in $officeApps) {
        $addinPath = "HKCU:\Software\Microsoft\Office\$($app.Name)\Addins\$progId"
        New-Item -Path $addinPath -Force | Out-Null
        Set-ItemProperty -Path $addinPath -Name "FriendlyName" -Value $app.FriendlyName
        Set-ItemProperty -Path $addinPath -Name "Description" -Value $app.Description
        Set-ItemProperty -Path $addinPath -Name "LoadBehavior" -Value 3 -Type DWord
        
        Write-Host "Registered add-in for $($app.Name)" -ForegroundColor Cyan
    }
    
    Write-Host ""
    Write-Host "Registration completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "To verify the installation:" -ForegroundColor Yellow
    Write-Host "1. Open Word, Excel, or PowerPoint" -ForegroundColor White
    Write-Host "2. Go to File > Options > Add-ins" -ForegroundColor White
    Write-Host "3. Look for 'MyCom Office Add-in' in the list" -ForegroundColor White
    Write-Host ""
    Write-Host "Note: You may need to restart Office applications for the add-in to appear." -ForegroundColor Yellow
    
} catch {
    Write-Host "Error during registration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
