HKCU
{
	NoRemove Software
	{
		NoRemove Classes
		{
			NoRemove CLSID
			{
				ForceRemove {A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47} = s 'MyCom Office Add-in'
				{
					InprocServer32 = s '%MODULE%'
					{
						val ThreadingModel = s 'Apartment'
					}
					TypeLib = s '{43b6042f-c348-4de4-b8ab-77dac9fd1cb0}'
					Version = s '1.0'
					ProgID = s 'MyCom.OfficeAddin.1'
					VersionIndependentProgID = s 'MyCom.OfficeAddin'
				}
			}
			ForceRemove MyCom.OfficeAddin.1 = s 'MyCom Office Add-in'
			{
				CLSID = s '{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}'
			}
			ForceRemove MyCom.OfficeAddin = s 'MyCom Office Add-in'
			{
				CLSID = s '{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}'
				CurVer = s 'MyCom.OfficeAddin.1'
			}
		}
	}
}

HKCU
{
	NoRemove Software
	{
		NoRemove Microsoft
		{
			NoRemove Office
			{
				NoRemove Word
				{
					NoRemove Addins
					{
						ForceRemove MyCom.OfficeAddin
						{
							val FriendlyName = s 'MyCom Office Add-in for Word'
							val Description = s 'Provides document statistics and formatting features'
							val LoadBehavior = d '00000003'
						}
					}
				}
				NoRemove Excel
				{
					NoRemove Addins
					{
						ForceRemove MyCom.OfficeAddin
						{
							val FriendlyName = s 'MyCom Office Add-in for Excel'
							val Description = s 'Provides worksheet statistics and chart generation features'
							val LoadBehavior = d '00000003'
						}
					}
				}
				NoRemove PowerPoint
				{
					NoRemove Addins
					{
						ForceRemove MyCom.OfficeAddin
						{
							val FriendlyName = s 'MyCom Office Add-in for PowerPoint'
							val Description = s 'Provides presentation statistics and slide management features'
							val LoadBehavior = d '00000003'
						}
					}
				}
			}
		}
	}
}
