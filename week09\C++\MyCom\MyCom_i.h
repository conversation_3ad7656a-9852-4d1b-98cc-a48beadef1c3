

/* this ALWAYS GENERATED file contains the definitions for the interfaces */


 /* File created by MIDL compiler version 8.01.0628 */
/* at Tue Jan 19 11:14:07 2038
 */
/* Compiler settings for MyCom.idl:
    Oicf, W1, Zp8, env=Win64 (32b run), target_arch=AMD64 8.01.0628 
    protocol : all , ms_ext, c_ext, robust
    error checks: allocation ref bounds_check enum stub_data 
    VC __declspec() decoration level: 
         __declspec(uuid()), __declspec(selectany), __declspec(novtable)
         DECLSPEC_UUID(), MIDL_INTERFACE()
*/
/* @@MIDL_FILE_HEADING(  ) */



/* verify that the <rpcndr.h> version is high enough to compile this file*/
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 500
#endif

#include "rpc.h"
#include "rpcndr.h"

#ifndef __RPCNDR_H_VERSION__
#error this stub requires an updated version of <rpcndr.h>
#endif /* __RPCNDR_H_VERSION__ */

#ifndef COM_NO_WINDOWS_H
#include "windows.h"
#include "ole2.h"
#endif /*COM_NO_WINDOWS_H*/

#ifndef __MyCom_i_h__
#define __MyCom_i_h__

#if defined(_MSC_VER) && (_MSC_VER >= 1020)
#pragma once
#endif

#ifndef DECLSPEC_XFGVIRT
#if defined(_CONTROL_FLOW_GUARD_XFG)
#define DECLSPEC_XFGVIRT(base, func) __declspec(xfg_virtual(base, func))
#else
#define DECLSPEC_XFGVIRT(base, func)
#endif
#endif

/* Forward Declarations */ 

#ifndef __IDTExtensibility2_FWD_DEFINED__
#define __IDTExtensibility2_FWD_DEFINED__
typedef interface IDTExtensibility2 IDTExtensibility2;

#endif 	/* __IDTExtensibility2_FWD_DEFINED__ */


#ifndef __OfficeAddin_FWD_DEFINED__
#define __OfficeAddin_FWD_DEFINED__

#ifdef __cplusplus
typedef class OfficeAddin OfficeAddin;
#else
typedef struct OfficeAddin OfficeAddin;
#endif /* __cplusplus */

#endif 	/* __OfficeAddin_FWD_DEFINED__ */


/* header files for imported files */
#include "oaidl.h"
#include "ocidl.h"

#ifdef __cplusplus
extern "C"{
#endif 


#ifndef __IDTExtensibility2_INTERFACE_DEFINED__
#define __IDTExtensibility2_INTERFACE_DEFINED__

/* interface IDTExtensibility2 */
/* [unique][nonextensible][dual][uuid][object] */ 


EXTERN_C const IID IID_IDTExtensibility2;

#if defined(__cplusplus) && !defined(CINTERFACE)
    
    MIDL_INTERFACE("B65AD801-ABAF-11D0-BB8B-00A0C90F2744")
    IDTExtensibility2 : public IDispatch
    {
    public:
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnConnection( 
            /* [in] */ IDispatch *Application,
            /* [in] */ long ConnectMode,
            /* [in] */ IDispatch *AddInInst,
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnDisconnection( 
            /* [in] */ long RemoveMode,
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnAddInsUpdate( 
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnStartupComplete( 
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnBeginShutdown( 
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE TriggerWordFeatures( void) = 0;
        
    };
    
    
#else 	/* C style interface */

    typedef struct IDTExtensibility2Vtbl
    {
        BEGIN_INTERFACE
        
        DECLSPEC_XFGVIRT(IUnknown, QueryInterface)
        HRESULT ( STDMETHODCALLTYPE *QueryInterface )( 
            IDTExtensibility2 * This,
            /* [in] */ REFIID riid,
            /* [annotation][iid_is][out] */ 
            _COM_Outptr_  void **ppvObject);
        
        DECLSPEC_XFGVIRT(IUnknown, AddRef)
        ULONG ( STDMETHODCALLTYPE *AddRef )( 
            IDTExtensibility2 * This);
        
        DECLSPEC_XFGVIRT(IUnknown, Release)
        ULONG ( STDMETHODCALLTYPE *Release )( 
            IDTExtensibility2 * This);
        
        DECLSPEC_XFGVIRT(IDispatch, GetTypeInfoCount)
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfoCount )( 
            IDTExtensibility2 * This,
            /* [out] */ UINT *pctinfo);
        
        DECLSPEC_XFGVIRT(IDispatch, GetTypeInfo)
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfo )( 
            IDTExtensibility2 * This,
            /* [in] */ UINT iTInfo,
            /* [in] */ LCID lcid,
            /* [out] */ ITypeInfo **ppTInfo);
        
        DECLSPEC_XFGVIRT(IDispatch, GetIDsOfNames)
        HRESULT ( STDMETHODCALLTYPE *GetIDsOfNames )( 
            IDTExtensibility2 * This,
            /* [in] */ REFIID riid,
            /* [size_is][in] */ LPOLESTR *rgszNames,
            /* [range][in] */ UINT cNames,
            /* [in] */ LCID lcid,
            /* [size_is][out] */ DISPID *rgDispId);
        
        DECLSPEC_XFGVIRT(IDispatch, Invoke)
        /* [local] */ HRESULT ( STDMETHODCALLTYPE *Invoke )( 
            IDTExtensibility2 * This,
            /* [annotation][in] */ 
            _In_  DISPID dispIdMember,
            /* [annotation][in] */ 
            _In_  REFIID riid,
            /* [annotation][in] */ 
            _In_  LCID lcid,
            /* [annotation][in] */ 
            _In_  WORD wFlags,
            /* [annotation][out][in] */ 
            _In_  DISPPARAMS *pDispParams,
            /* [annotation][out] */ 
            _Out_opt_  VARIANT *pVarResult,
            /* [annotation][out] */ 
            _Out_opt_  EXCEPINFO *pExcepInfo,
            /* [annotation][out] */ 
            _Out_opt_  UINT *puArgErr);
        
        DECLSPEC_XFGVIRT(IDTExtensibility2, OnConnection)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnConnection )( 
            IDTExtensibility2 * This,
            /* [in] */ IDispatch *Application,
            /* [in] */ long ConnectMode,
            /* [in] */ IDispatch *AddInInst,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IDTExtensibility2, OnDisconnection)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnDisconnection )( 
            IDTExtensibility2 * This,
            /* [in] */ long RemoveMode,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IDTExtensibility2, OnAddInsUpdate)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnAddInsUpdate )( 
            IDTExtensibility2 * This,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IDTExtensibility2, OnStartupComplete)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnStartupComplete )( 
            IDTExtensibility2 * This,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IDTExtensibility2, OnBeginShutdown)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnBeginShutdown )( 
            IDTExtensibility2 * This,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IDTExtensibility2, TriggerWordFeatures)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *TriggerWordFeatures )( 
            IDTExtensibility2 * This);
        
        END_INTERFACE
    } IDTExtensibility2Vtbl;

    interface IDTExtensibility2
    {
        CONST_VTBL struct IDTExtensibility2Vtbl *lpVtbl;
    };

    

#ifdef COBJMACROS


#define IDTExtensibility2_QueryInterface(This,riid,ppvObject)	\
    ( (This)->lpVtbl -> QueryInterface(This,riid,ppvObject) ) 

#define IDTExtensibility2_AddRef(This)	\
    ( (This)->lpVtbl -> AddRef(This) ) 

#define IDTExtensibility2_Release(This)	\
    ( (This)->lpVtbl -> Release(This) ) 


#define IDTExtensibility2_GetTypeInfoCount(This,pctinfo)	\
    ( (This)->lpVtbl -> GetTypeInfoCount(This,pctinfo) ) 

#define IDTExtensibility2_GetTypeInfo(This,iTInfo,lcid,ppTInfo)	\
    ( (This)->lpVtbl -> GetTypeInfo(This,iTInfo,lcid,ppTInfo) ) 

#define IDTExtensibility2_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)	\
    ( (This)->lpVtbl -> GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) ) 

#define IDTExtensibility2_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)	\
    ( (This)->lpVtbl -> Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) ) 


#define IDTExtensibility2_OnConnection(This,Application,ConnectMode,AddInInst,custom)	\
    ( (This)->lpVtbl -> OnConnection(This,Application,ConnectMode,AddInInst,custom) ) 

#define IDTExtensibility2_OnDisconnection(This,RemoveMode,custom)	\
    ( (This)->lpVtbl -> OnDisconnection(This,RemoveMode,custom) ) 

#define IDTExtensibility2_OnAddInsUpdate(This,custom)	\
    ( (This)->lpVtbl -> OnAddInsUpdate(This,custom) ) 

#define IDTExtensibility2_OnStartupComplete(This,custom)	\
    ( (This)->lpVtbl -> OnStartupComplete(This,custom) ) 

#define IDTExtensibility2_OnBeginShutdown(This,custom)	\
    ( (This)->lpVtbl -> OnBeginShutdown(This,custom) ) 

#define IDTExtensibility2_TriggerWordFeatures(This)	\
    ( (This)->lpVtbl -> TriggerWordFeatures(This) ) 

#endif /* COBJMACROS */


#endif 	/* C style interface */




#endif 	/* __IDTExtensibility2_INTERFACE_DEFINED__ */



#ifndef __MyComLib_LIBRARY_DEFINED__
#define __MyComLib_LIBRARY_DEFINED__

/* library MyComLib */
/* [helpstring][version][uuid] */ 


EXTERN_C const IID LIBID_MyComLib;

EXTERN_C const CLSID CLSID_OfficeAddin;

#ifdef __cplusplus

class DECLSPEC_UUID("A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47")
OfficeAddin;
#endif
#endif /* __MyComLib_LIBRARY_DEFINED__ */

/* Additional Prototypes for ALL interfaces */

unsigned long             __RPC_USER  LPSAFEARRAY_UserSize(     unsigned long *, unsigned long            , LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserMarshal(  unsigned long *, unsigned char *, LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserUnmarshal(unsigned long *, unsigned char *, LPSAFEARRAY * ); 
void                      __RPC_USER  LPSAFEARRAY_UserFree(     unsigned long *, LPSAFEARRAY * ); 

unsigned long             __RPC_USER  LPSAFEARRAY_UserSize64(     unsigned long *, unsigned long            , LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserMarshal64(  unsigned long *, unsigned char *, LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserUnmarshal64(unsigned long *, unsigned char *, LPSAFEARRAY * ); 
void                      __RPC_USER  LPSAFEARRAY_UserFree64(     unsigned long *, LPSAFEARRAY * ); 

/* end of Additional Prototypes */

#ifdef __cplusplus
}
#endif

#endif


