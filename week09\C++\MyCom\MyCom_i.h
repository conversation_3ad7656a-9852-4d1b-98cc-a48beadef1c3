

/* this ALWAYS GENERATED file contains the definitions for the interfaces */


 /* File created by MIDL compiler version 8.01.0628 */
/* at Tue Jan 19 11:14:07 2038
 */
/* Compiler settings for MyCom.idl:
    Oicf, W1, Zp8, env=Win64 (32b run), target_arch=AMD64 8.01.0628 
    protocol : all , ms_ext, c_ext, robust
    error checks: allocation ref bounds_check enum stub_data 
    VC __declspec() decoration level: 
         __declspec(uuid()), __declspec(selectany), __declspec(novtable)
         DECLSPEC_UUID(), MIDL_INTERFACE()
*/
/* @@MIDL_FILE_HEADING(  ) */



/* verify that the <rpcndr.h> version is high enough to compile this file*/
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 500
#endif

#include "rpc.h"
#include "rpcndr.h"

#ifndef __RPCNDR_H_VERSION__
#error this stub requires an updated version of <rpcndr.h>
#endif /* __RPCNDR_H_VERSION__ */

#ifndef COM_NO_WINDOWS_H
#include "windows.h"
#include "ole2.h"
#endif /*COM_NO_WINDOWS_H*/

#ifndef __MyCom_i_h__
#define __MyCom_i_h__

#if defined(_MSC_VER) && (_MSC_VER >= 1020)
#pragma once
#endif

#ifndef DECLSPEC_XFGVIRT
#if defined(_CONTROL_FLOW_GUARD_XFG)
#define DECLSPEC_XFGVIRT(base, func) __declspec(xfg_virtual(base, func))
#else
#define DECLSPEC_XFGVIRT(base, func)
#endif
#endif

/* Forward Declarations */ 

#ifndef __IOfficeAddin_FWD_DEFINED__
#define __IOfficeAddin_FWD_DEFINED__
typedef interface IOfficeAddin IOfficeAddin;

#endif 	/* __IOfficeAddin_FWD_DEFINED__ */


#ifndef __OfficeAddin_FWD_DEFINED__
#define __OfficeAddin_FWD_DEFINED__

#ifdef __cplusplus
typedef class OfficeAddin OfficeAddin;
#else
typedef struct OfficeAddin OfficeAddin;
#endif /* __cplusplus */

#endif 	/* __OfficeAddin_FWD_DEFINED__ */


/* header files for imported files */
#include "oaidl.h"
#include "ocidl.h"

#ifdef __cplusplus
extern "C"{
#endif 


#ifndef __IOfficeAddin_INTERFACE_DEFINED__
#define __IOfficeAddin_INTERFACE_DEFINED__

/* interface IOfficeAddin */
/* [unique][nonextensible][dual][uuid][object] */ 


EXTERN_C const IID IID_IOfficeAddin;

#if defined(__cplusplus) && !defined(CINTERFACE)
    
    MIDL_INTERFACE("B621E782-C026-4E18-8271-5448D6B2DE48")
    IOfficeAddin : public IDispatch
    {
    public:
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnConnection( 
            /* [in] */ IDispatch *Application,
            /* [in] */ long ConnectMode,
            /* [in] */ IDispatch *AddInInst,
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnDisconnection( 
            /* [in] */ long RemoveMode,
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnAddInsUpdate( 
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnStartupComplete( 
            /* [in] */ SAFEARRAY * *custom) = 0;
        
        virtual /* [id] */ HRESULT STDMETHODCALLTYPE OnBeginShutdown( 
            /* [in] */ SAFEARRAY * *custom) = 0;
        
    };
    
    
#else 	/* C style interface */

    typedef struct IOfficeAddinVtbl
    {
        BEGIN_INTERFACE
        
        DECLSPEC_XFGVIRT(IUnknown, QueryInterface)
        HRESULT ( STDMETHODCALLTYPE *QueryInterface )( 
            IOfficeAddin * This,
            /* [in] */ REFIID riid,
            /* [annotation][iid_is][out] */ 
            _COM_Outptr_  void **ppvObject);
        
        DECLSPEC_XFGVIRT(IUnknown, AddRef)
        ULONG ( STDMETHODCALLTYPE *AddRef )( 
            IOfficeAddin * This);
        
        DECLSPEC_XFGVIRT(IUnknown, Release)
        ULONG ( STDMETHODCALLTYPE *Release )( 
            IOfficeAddin * This);
        
        DECLSPEC_XFGVIRT(IDispatch, GetTypeInfoCount)
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfoCount )( 
            IOfficeAddin * This,
            /* [out] */ UINT *pctinfo);
        
        DECLSPEC_XFGVIRT(IDispatch, GetTypeInfo)
        HRESULT ( STDMETHODCALLTYPE *GetTypeInfo )( 
            IOfficeAddin * This,
            /* [in] */ UINT iTInfo,
            /* [in] */ LCID lcid,
            /* [out] */ ITypeInfo **ppTInfo);
        
        DECLSPEC_XFGVIRT(IDispatch, GetIDsOfNames)
        HRESULT ( STDMETHODCALLTYPE *GetIDsOfNames )( 
            IOfficeAddin * This,
            /* [in] */ REFIID riid,
            /* [size_is][in] */ LPOLESTR *rgszNames,
            /* [range][in] */ UINT cNames,
            /* [in] */ LCID lcid,
            /* [size_is][out] */ DISPID *rgDispId);
        
        DECLSPEC_XFGVIRT(IDispatch, Invoke)
        /* [local] */ HRESULT ( STDMETHODCALLTYPE *Invoke )( 
            IOfficeAddin * This,
            /* [annotation][in] */ 
            _In_  DISPID dispIdMember,
            /* [annotation][in] */ 
            _In_  REFIID riid,
            /* [annotation][in] */ 
            _In_  LCID lcid,
            /* [annotation][in] */ 
            _In_  WORD wFlags,
            /* [annotation][out][in] */ 
            _In_  DISPPARAMS *pDispParams,
            /* [annotation][out] */ 
            _Out_opt_  VARIANT *pVarResult,
            /* [annotation][out] */ 
            _Out_opt_  EXCEPINFO *pExcepInfo,
            /* [annotation][out] */ 
            _Out_opt_  UINT *puArgErr);
        
        DECLSPEC_XFGVIRT(IOfficeAddin, OnConnection)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnConnection )( 
            IOfficeAddin * This,
            /* [in] */ IDispatch *Application,
            /* [in] */ long ConnectMode,
            /* [in] */ IDispatch *AddInInst,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IOfficeAddin, OnDisconnection)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnDisconnection )( 
            IOfficeAddin * This,
            /* [in] */ long RemoveMode,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IOfficeAddin, OnAddInsUpdate)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnAddInsUpdate )( 
            IOfficeAddin * This,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IOfficeAddin, OnStartupComplete)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnStartupComplete )( 
            IOfficeAddin * This,
            /* [in] */ SAFEARRAY * *custom);
        
        DECLSPEC_XFGVIRT(IOfficeAddin, OnBeginShutdown)
        /* [id] */ HRESULT ( STDMETHODCALLTYPE *OnBeginShutdown )( 
            IOfficeAddin * This,
            /* [in] */ SAFEARRAY * *custom);
        
        END_INTERFACE
    } IOfficeAddinVtbl;

    interface IOfficeAddin
    {
        CONST_VTBL struct IOfficeAddinVtbl *lpVtbl;
    };

    

#ifdef COBJMACROS


#define IOfficeAddin_QueryInterface(This,riid,ppvObject)	\
    ( (This)->lpVtbl -> QueryInterface(This,riid,ppvObject) ) 

#define IOfficeAddin_AddRef(This)	\
    ( (This)->lpVtbl -> AddRef(This) ) 

#define IOfficeAddin_Release(This)	\
    ( (This)->lpVtbl -> Release(This) ) 


#define IOfficeAddin_GetTypeInfoCount(This,pctinfo)	\
    ( (This)->lpVtbl -> GetTypeInfoCount(This,pctinfo) ) 

#define IOfficeAddin_GetTypeInfo(This,iTInfo,lcid,ppTInfo)	\
    ( (This)->lpVtbl -> GetTypeInfo(This,iTInfo,lcid,ppTInfo) ) 

#define IOfficeAddin_GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId)	\
    ( (This)->lpVtbl -> GetIDsOfNames(This,riid,rgszNames,cNames,lcid,rgDispId) ) 

#define IOfficeAddin_Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr)	\
    ( (This)->lpVtbl -> Invoke(This,dispIdMember,riid,lcid,wFlags,pDispParams,pVarResult,pExcepInfo,puArgErr) ) 


#define IOfficeAddin_OnConnection(This,Application,ConnectMode,AddInInst,custom)	\
    ( (This)->lpVtbl -> OnConnection(This,Application,ConnectMode,AddInInst,custom) ) 

#define IOfficeAddin_OnDisconnection(This,RemoveMode,custom)	\
    ( (This)->lpVtbl -> OnDisconnection(This,RemoveMode,custom) ) 

#define IOfficeAddin_OnAddInsUpdate(This,custom)	\
    ( (This)->lpVtbl -> OnAddInsUpdate(This,custom) ) 

#define IOfficeAddin_OnStartupComplete(This,custom)	\
    ( (This)->lpVtbl -> OnStartupComplete(This,custom) ) 

#define IOfficeAddin_OnBeginShutdown(This,custom)	\
    ( (This)->lpVtbl -> OnBeginShutdown(This,custom) ) 

#endif /* COBJMACROS */


#endif 	/* C style interface */




#endif 	/* __IOfficeAddin_INTERFACE_DEFINED__ */



#ifndef __MyComLib_LIBRARY_DEFINED__
#define __MyComLib_LIBRARY_DEFINED__

/* library MyComLib */
/* [helpstring][version][uuid] */ 


EXTERN_C const IID LIBID_MyComLib;

EXTERN_C const CLSID CLSID_OfficeAddin;

#ifdef __cplusplus

class DECLSPEC_UUID("A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47")
OfficeAddin;
#endif
#endif /* __MyComLib_LIBRARY_DEFINED__ */

/* Additional Prototypes for ALL interfaces */

unsigned long             __RPC_USER  LPSAFEARRAY_UserSize(     unsigned long *, unsigned long            , LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserMarshal(  unsigned long *, unsigned char *, LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserUnmarshal(unsigned long *, unsigned char *, LPSAFEARRAY * ); 
void                      __RPC_USER  LPSAFEARRAY_UserFree(     unsigned long *, LPSAFEARRAY * ); 

unsigned long             __RPC_USER  LPSAFEARRAY_UserSize64(     unsigned long *, unsigned long            , LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserMarshal64(  unsigned long *, unsigned char *, LPSAFEARRAY * ); 
unsigned char * __RPC_USER  LPSAFEARRAY_UserUnmarshal64(unsigned long *, unsigned char *, LPSAFEARRAY * ); 
void                      __RPC_USER  LPSAFEARRAY_UserFree64(     unsigned long *, LPSAFEARRAY * ); 

/* end of Additional Prototypes */

#ifdef __cplusplus
}
#endif

#endif


