# PowerShell script to unregister MyCom Office Add-in
# This script removes the add-in registration for the current user

Write-Host "Unregistering MyCom Office Add-in for current user..." -ForegroundColor Yellow
Write-Host ""

try {
    $clsid = "{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}"
    $progId = "MyCom.OfficeAddin"
    $progIdVersioned = "MyCom.OfficeAddin.1"
    
    # Remove CLSID registry entries
    $clsidPath = "HKCU:\Software\Classes\CLSID\$clsid"
    if (Test-Path $clsidPath) {
        Remove-Item -Path $clsidPath -Recurse -Force
        Write-Host "Removed CLSID registration" -ForegroundColor Green
    }
    
    # Remove ProgID entries
    $progIdPath = "HKCU:\Software\Classes\$progIdVersioned"
    if (Test-Path $progIdPath) {
        Remove-Item -Path $progIdPath -Recurse -Force
        Write-Host "Removed ProgID registration" -ForegroundColor Green
    }
    
    $progIdViPath = "HKCU:\Software\Classes\$progId"
    if (Test-Path $progIdViPath) {
        Remove-Item -Path $progIdViPath -Recurse -Force
        Write-Host "Removed version-independent ProgID registration" -ForegroundColor Green
    }
    
    # Remove Office Add-ins
    $officeApps = @("Word", "Excel", "PowerPoint")
    
    foreach ($app in $officeApps) {
        $addinPath = "HKCU:\Software\Microsoft\Office\$app\Addins\$progId"
        if (Test-Path $addinPath) {
            Remove-Item -Path $addinPath -Recurse -Force
            Write-Host "Removed add-in registration for $app" -ForegroundColor Cyan
        }
    }
    
    Write-Host ""
    Write-Host "Unregistration completed successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "The MyCom Office Add-in has been removed from:" -ForegroundColor Yellow
    Write-Host "- COM component registry" -ForegroundColor White
    Write-Host "- Word add-ins" -ForegroundColor White
    Write-Host "- Excel add-ins" -ForegroundColor White
    Write-Host "- PowerPoint add-ins" -ForegroundColor White
    Write-Host ""
    Write-Host "Note: You may need to restart Office applications for the changes to take effect." -ForegroundColor Yellow
    
} catch {
    Write-Host "Error during unregistration: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
