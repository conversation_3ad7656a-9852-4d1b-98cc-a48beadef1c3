# MyCom Office Add-in 故障排除指南

## 问题 1: 在 Office 设置中无法看到插件

### 可能原因和解决方案

#### 1. 检查注册状态
```powershell
# 运行测试脚本
.\TestAddin.ps1
```

确保所有项目都显示 ✓，特别是 LoadBehavior 应该为 3。

#### 2. 检查 Office 版本兼容性
- 确保使用的是 64 位 Office（我们编译的是 x64 版本）
- 如果使用 32 位 Office，需要重新编译为 x86 版本

#### 3. 检查 Office 安全设置
1. 打开 Word/Excel/PowerPoint
2. 转到 `文件 > 选项 > 信任中心 > 信任中心设置`
3. 选择 `加载项`
4. 确保以下选项已启用：
   - "要求应用程序加载项由受信任的发布者签名"（取消勾选）
   - "禁用所有应用程序加载项的通知"（取消勾选）

#### 4. 手动检查注册表
打开注册表编辑器 (regedit) 并检查：

**COM 注册:**
```
HKEY_CURRENT_USER\Software\Classes\CLSID\{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}
```

**Office 插件注册:**
```
HKEY_CURRENT_USER\Software\Microsoft\Office\Word\Addins\MyCom.OfficeAddin
HKEY_CURRENT_USER\Software\Microsoft\Office\Excel\Addins\MyCom.OfficeAddin
HKEY_CURRENT_USER\Software\Microsoft\Office\PowerPoint\Addins\MyCom.OfficeAddin
```

确保 LoadBehavior 值为 3 (DWORD)。

## 问题 2: 没有看到消息框

### 可能原因和解决方案

#### 1. 检查插件是否被调用
```powershell
# 检查日志文件
.\CheckLogs.ps1
```

如果日志文件存在且有 "OnConnection called!" 消息，说明插件被加载了。

#### 2. Office 启动顺序
1. **完全关闭所有 Office 应用程序**
2. 等待几秒钟
3. 重新打开 Word、Excel 或 PowerPoint
4. 观察是否有消息框出现

#### 3. 检查消息框是否被阻止
- 消息框可能被 Windows 或安全软件阻止
- 尝试在任务栏中查找闪烁的应用程序图标

#### 4. 调试模式测试
如果仍然没有消息框，可以尝试：

1. 打开 Visual Studio
2. 附加到 Office 进程进行调试：
   - `调试 > 附加到进程`
   - 选择 WINWORD.EXE、EXCEL.EXE 或 POWERPNT.EXE
   - 在 `OnConnection` 方法中设置断点

## 问题 3: LoadBehavior 值不正确

### 解决方案

如果 LoadBehavior 不是 3，手动修复：

```powershell
# 设置正确的 LoadBehavior
$apps = @("Word", "Excel", "PowerPoint")
foreach ($app in $apps) {
    $path = "HKCU:\Software\Microsoft\Office\$app\Addins\MyCom.OfficeAddin"
    if (Test-Path $path) {
        Set-ItemProperty -Path $path -Name "LoadBehavior" -Value 3 -Type DWord
        Write-Host "Fixed LoadBehavior for $app"
    }
}
```

## LoadBehavior 值说明

- **0**: 未加载，已禁用
- **1**: 已加载
- **2**: 启动时加载，当前未加载
- **3**: 启动时加载，当前已加载 ✓
- **8**: 按需加载
- **16**: 首次加载时加载

## 常见错误代码

### 注册错误
- **0x80070005**: 访问被拒绝 - 尝试以管理员身份运行
- **0x80020009**: 异常 - 检查 DLL 依赖项和接口实现
- **0x80040200**: 无效的类字符串 - 检查 CLSID 是否正确

### Office 加载错误
- **0x80040154**: 类未注册 - 重新运行注册脚本
- **0x80070002**: 找不到文件 - 检查 DLL 路径是否正确

## 完整测试流程

1. **重新编译项目**
   ```
   msbuild MyCom.sln /p:Configuration=Debug /p:Platform=x64 /t:Clean
   msbuild MyCom.sln /p:Configuration=Debug /p:Platform=x64
   ```

2. **卸载旧版本**
   ```
   .\UnregisterAddin.ps1
   ```

3. **重新注册**
   ```
   .\RegisterAddin.ps1
   ```

4. **验证注册**
   ```
   .\TestAddin.ps1
   ```

5. **关闭所有 Office 应用程序**

6. **重新打开 Office 应用程序**

7. **检查日志**
   ```
   .\CheckLogs.ps1
   ```

## 如果仍然无法工作

1. 检查 Windows 事件查看器中的错误
2. 尝试使用 Process Monitor 监控文件和注册表访问
3. 确认 Office 版本和架构（32位 vs 64位）
4. 尝试在不同的 Office 应用程序中测试
5. 检查是否有其他安全软件阻止 DLL 加载

## 联系支持

如果按照以上步骤仍然无法解决问题，请提供：
- Windows 版本
- Office 版本和架构
- 错误消息截图
- 注册测试结果
- 日志文件内容
