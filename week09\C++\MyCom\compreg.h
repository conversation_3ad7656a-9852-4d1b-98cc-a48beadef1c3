// compreg.h : Declaration of the COfficeAddin

#pragma once

#include "resource.h"       // main symbols
#include "MyCom_i.h"

#if defined(_WIN32_WCE) && !defined(_CE_DCOM) && !defined(_CE_ALLOW_SINGLE_THREADED_OBJECTS_IN_MTA)
#error "Single-threaded COM objects are not properly supported on Windows CE platform, such as the Windows Mobile platforms that do not include full DCOM support. Define _CE_ALLOW_SINGLE_THREADED_OBJECTS_IN_MTA to force ATL to support creating single-threaded COM objects and allow use of its single-threaded COM object implementations. The threading model in your rgs file was set to 'Free' as that is the only threading model supported in non DCOM Windows CE platforms."
#endif

using namespace ATL;

// COfficeAddin
class ATL_NO_VTABLE COfficeAddin :
	public CComObjectRootEx<CComSingleThreadModel>,
	public CComCoClass<COfficeAddin, &CLSID_OfficeAddin>,
	public IDispatchImpl<IDTExtensibility2, &IID_IDTExtensibility2, &LIBID_MyComLib, /*wMajor =*/ 1, /*wMinor =*/ 0>
{
public:
	COfficeAddin()
	{
		m_pApplication = nullptr;
		m_pAddInInstance = nullptr;
		m_pWordApp = nullptr;
		m_pCommandBar = nullptr;
		m_pFormatButton = nullptr;
		m_pDocumentEvents = nullptr;
		m_bUICreated = false;
	}

DECLARE_REGISTRY_RESOURCEID(IDR_OFFICEADDIN)

BEGIN_COM_MAP(COfficeAddin)
	COM_INTERFACE_ENTRY(IDTExtensibility2)
	COM_INTERFACE_ENTRY(IDispatch)
END_COM_MAP()

	DECLARE_PROTECT_FINAL_CONSTRUCT()

	HRESULT FinalConstruct()
	{
		return S_OK;
	}

	void FinalRelease()
	{
		if (m_pDocumentEvents)
		{
			m_pDocumentEvents->Release();
			m_pDocumentEvents = nullptr;
		}
		if (m_pFormatButton)
		{
			m_pFormatButton->Release();
			m_pFormatButton = nullptr;
		}
		if (m_pCommandBar)
		{
			m_pCommandBar->Release();
			m_pCommandBar = nullptr;
		}
		if (m_pWordApp)
		{
			m_pWordApp->Release();
			m_pWordApp = nullptr;
		}
		if (m_pApplication)
		{
			m_pApplication->Release();
			m_pApplication = nullptr;
		}
		if (m_pAddInInstance)
		{
			m_pAddInInstance->Release();
			m_pAddInInstance = nullptr;
		}
	}

public:
	// IDTExtensibility2 Methods
	STDMETHOD(OnConnection)(IDispatch* Application, long ConnectMode, IDispatch* AddInInst, SAFEARRAY** custom);
	STDMETHOD(OnDisconnection)(long RemoveMode, SAFEARRAY** custom);
	STDMETHOD(OnAddInsUpdate)(SAFEARRAY** custom);
	STDMETHOD(OnStartupComplete)(SAFEARRAY** custom);
	STDMETHOD(OnBeginShutdown)(SAFEARRAY** custom);

	// Public methods for manual triggering
	STDMETHOD(TriggerWordFeatures)();

private:
	IDispatch* m_pApplication;
	IDispatch* m_pAddInInstance;
	IDispatch* m_pWordApp;
	IDispatch* m_pCommandBar;
	IDispatch* m_pFormatButton;
	IDispatch* m_pDocumentEvents;
	bool m_bUICreated;

	// Helper methods
	HRESULT DetectOfficeApplication();
	HRESULT SetupWordFeatures();
	HRESULT SetupExcelFeatures();
	HRESULT SetupPowerPointFeatures();

	// Word-specific methods
	HRESULT SetupWordEventHandlers();
	HRESULT OnDocumentOpen();
	HRESULT CreateWordToolbar();
	HRESULT AddToolbarButtons();
	HRESULT ShowWordStatistics();
	HRESULT FormatSelectedText();
	HRESULT CountChineseAndEnglishWords(const wchar_t* text, int& chineseCount, int& englishWords);
	HRESULT GetDocumentText(BSTR* documentText);
};

OBJECT_ENTRY_AUTO(CLSID_OfficeAddin, COfficeAddin)

