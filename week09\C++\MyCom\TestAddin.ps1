# PowerShell script to test MyCom Office Add-in registration
# This script checks if the add-in is properly registered

Write-Host "Testing MyCom Office Add-in registration..." -ForegroundColor Green
Write-Host ""

$clsid = "{A4F7A4DA-D83F-4A24-8A93-6B8D4B2C1E47}"
$progId = "MyCom.OfficeAddin"
$progIdVersioned = "MyCom.OfficeAddin.1"

$allGood = $true

# Test CLSID registration
$clsidPath = "HKCU:\Software\Classes\CLSID\$clsid"
if (Test-Path $clsidPath) {
    Write-Host "✓ CLSID registration found" -ForegroundColor Green
    
    $inprocPath = "$clsidPath\InprocServer32"
    if (Test-Path $inprocPath) {
        $dllPath = Get-ItemProperty -Path $inprocPath -Name "(Default)" -ErrorAction SilentlyContinue
        if ($dllPath) {
            Write-Host "  DLL Path: $($dllPath.'(Default)')" -ForegroundColor Cyan
            if (Test-Path $dllPath.'(Default)') {
                Write-Host "  ✓ DLL file exists" -ForegroundColor Green
            } else {
                Write-Host "  ✗ DLL file not found" -ForegroundColor Red
                $allGood = $false
            }
        }
    }
} else {
    Write-Host "✗ CLSID registration not found" -ForegroundColor Red
    $allGood = $false
}

# Test ProgID registrations
$progIdPath = "HKCU:\Software\Classes\$progIdVersioned"
if (Test-Path $progIdPath) {
    Write-Host "✓ Versioned ProgID registration found" -ForegroundColor Green
} else {
    Write-Host "✗ Versioned ProgID registration not found" -ForegroundColor Red
    $allGood = $false
}

$progIdViPath = "HKCU:\Software\Classes\$progId"
if (Test-Path $progIdViPath) {
    Write-Host "✓ Version-independent ProgID registration found" -ForegroundColor Green
} else {
    Write-Host "✗ Version-independent ProgID registration not found" -ForegroundColor Red
    $allGood = $false
}

# Test Office Add-ins
$officeApps = @("Word", "Excel", "PowerPoint")
Write-Host ""
Write-Host "Office Add-in registrations:" -ForegroundColor Yellow

foreach ($app in $officeApps) {
    $addinPath = "HKCU:\Software\Microsoft\Office\$app\Addins\$progId"
    if (Test-Path $addinPath) {
        Write-Host "✓ $app add-in registration found" -ForegroundColor Green
        
        $loadBehavior = Get-ItemProperty -Path $addinPath -Name "LoadBehavior" -ErrorAction SilentlyContinue
        if ($loadBehavior) {
            Write-Host "  LoadBehavior: $($loadBehavior.LoadBehavior)" -ForegroundColor Cyan
        }
    } else {
        Write-Host "✗ $app add-in registration not found" -ForegroundColor Red
        $allGood = $false
    }
}

Write-Host ""
if ($allGood) {
    Write-Host "🎉 All registrations are in place!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Yellow
    Write-Host "1. Open Word, Excel, or PowerPoint" -ForegroundColor White
    Write-Host "2. The add-in should load automatically and show a message box" -ForegroundColor White
    Write-Host "3. Check File > Options > Add-ins to see the add-in listed" -ForegroundColor White
} else {
    Write-Host "❌ Some registrations are missing!" -ForegroundColor Red
    Write-Host "Please run RegisterAddin.ps1 to register the add-in." -ForegroundColor Yellow
}
